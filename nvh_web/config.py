import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'nvh-system-secret-key-2024'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'nvh_data'
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': False
    }
    
    # Keycloak配置
    KEYCLOAK_FRONTEND_CLIENT_ID = 'front'
    KEYCLOAK_FRONTEND_CLIENT_SECRET = 'frontend-secret'
    KEYCLOAK_BACKEND_CLIENT_ID = 'backend'
    KEYCLOAK_BACKEND_CLIENT_SECRET = '8545c061-7cf7-41e5-b92b-e6769a6a75b8'
    KEYCLOAK_SERVER_METADATA_URL = 'https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration'
    KEYCLOAK_CLIENT_KWARGS = {
        'scope': 'openid email profile'
    }

    # 自动认证配置
    AUTO_AUTH_ENABLED = True  # 启用自动认证
    AUTH_EXCLUDED_PATHS = [    # 排除认证的路径
        '/static',
        '/favicon.ico',
        '/health',
        '/_health',
        '/auth/callback'
    ]
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'csv', 'xlsx'}

# 简化配置，只使用一个环境
Config.DEBUG = True
