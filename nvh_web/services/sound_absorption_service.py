from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel, 
    MaterialModel, 
    MaterialManufacturerModel, 
    SoundAbsorptionCoefficientModel
)
import csv
import io

class SoundAbsorptionService:
    """吸音系数业务逻辑服务"""
    
    def get_part_list(self):
        """获取零件列表"""
        parts = SoundInsulationPartModel.get_parts_with_data()
        return [{'name': part.part_name, 'description': part.description} for part in parts]
    
    def get_material_list(self, part_name=None):
        """获取材料列表"""
        if part_name:
            materials = MaterialModel.get_materials_by_part_name(part_name)
        else:
            materials = MaterialModel.get_all_materials()

        # 按材料名称分组，每个材料名称只返回一次
        material_dict = {}
        for material in materials:
            if material.material_name not in material_dict:
                material_dict[material.material_name] = {
                    'name': material.material_name,
                    'description': material.description
                }

        return list(material_dict.values())
    
    def get_weight_list(self, part_name, material_name):
        """获取克重列表"""
        weights = SoundAbsorptionCoefficientModel.get_weights_by_part_material(part_name, material_name)
        return [{'weight': weight, 'display': f"{weight}g/m²"} for weight in weights]
    
    def get_absorption_data(self, part_name, material_name, weight):
        """获取吸音系数数据"""
        try:
            data = SoundAbsorptionCoefficientModel.get_data_by_conditions(part_name, material_name, weight)
            
            if not data:
                return None
            
            # 获取频率数据
            test_data = data.get_test_frequency_data()
            target_data = data.get_target_frequency_data()
            
            # 构建表格数据
            table_data = []
            freq_labels = SoundAbsorptionCoefficientModel.get_frequency_labels()
            
            for freq in freq_labels:
                test_value = test_data.get(freq)
                target_value = target_data.get(freq)
                
                # 判断是否达标（测试值 >= 目标值）
                status = None
                if test_value is not None and target_value is not None:
                    status = 'success' if test_value >= target_value else 'danger'
                
                table_data.append({
                    'frequency': freq,
                    'test_value': test_value,
                    'target_value': target_value,
                    'status': status
                })
            
            # 构建图表数据
            chart_data = {
                'frequencies': freq_labels,
                'test_values': [test_data.get(freq) for freq in freq_labels],
                'target_values': [target_data.get(freq) for freq in freq_labels]
            }
            
            # 基础信息
            basic_info = {
                'part_name': data.part_name,
                'material_name': data.material_name,
                'material_manufacturer': data.manufacturer_name,
                'test_institution': data.test_institution,
                'thickness': float(data.thickness) if data.thickness else None,
                'weight': float(data.weight),
                'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                'test_location': data.test_location,
                'test_engineer': data.test_engineer,
                'test_image_path': data.test_image_path,
                'remarks': data.remarks
            }
            
            return {
                'basic_info': basic_info,
                'table_data': table_data,
                'chart_data': chart_data
            }
            
        except Exception as e:
            raise Exception(f"获取吸音系数数据失败: {str(e)}")
    
    def get_multi_weight_comparison(self, part_name, material_name, weights):
        """获取多克重对比数据"""
        try:
            comparison_data = {
                'basic_info': {
                    'part_name': part_name,
                    'material_name': material_name
                },
                'table_data': [],
                'chart_data': {
                    'frequencies': SoundAbsorptionCoefficientModel.get_frequency_labels(),
                    'series': []
                }
            }
            
            freq_labels = SoundAbsorptionCoefficientModel.get_frequency_labels()
            
            # 初始化表格数据
            for freq in freq_labels:
                row = {'frequency': freq}
                comparison_data['table_data'].append(row)
            
            # 获取每个克重的数据
            for weight in weights:
                data = SoundAbsorptionCoefficientModel.get_data_by_conditions(part_name, material_name, weight)
                
                if data:
                    test_data = data.get_test_frequency_data()
                    target_data = data.get_target_frequency_data()
                    
                    # 添加到表格数据
                    for i, freq in enumerate(freq_labels):
                        test_value = test_data.get(freq)
                        target_value = target_data.get(freq)
                        
                        comparison_data['table_data'][i][f'test_{weight}'] = test_value
                        comparison_data['table_data'][i][f'target_{weight}'] = target_value
                    
                    # 添加到图表数据
                    comparison_data['chart_data']['series'].append({
                        'name': f'{weight}g/m²测试值',
                        'type': 'line',
                        'data': [test_data.get(freq) for freq in freq_labels],
                        'lineStyle': {'type': 'solid'},
                        'symbol': 'circle'
                    })
                    
                    comparison_data['chart_data']['series'].append({
                        'name': f'{weight}g/m²目标值',
                        'type': 'line',
                        'data': [target_data.get(freq) for freq in freq_labels],
                        'lineStyle': {'type': 'dashed'},
                        'symbol': 'triangle'
                    })
            
            return comparison_data
            
        except Exception as e:
            raise Exception(f"获取多克重对比数据失败: {str(e)}")
    
    def get_test_image_info(self, part_name, material_name, weight):
        """获取测试图片信息"""
        try:
            data = SoundAbsorptionCoefficientModel.get_data_by_conditions(part_name, material_name, weight)
            
            if not data:
                return None
            
            return {
                'part_name': data.part_name,
                'material_name': data.material_name,
                'weight': f"{float(data.weight)}g/m²",
                'test_image_path': data.test_image_path or '',
                'test_date': data.test_date.strftime('%Y-%m-%d') if data.test_date else '',
                'test_engineer': data.test_engineer or '',
                'test_location': data.test_location or '',
                'test_institution': data.test_institution or '',
                'remarks': data.remarks or ''
            }
            
        except Exception as e:
            raise Exception(f"获取测试图片信息失败: {str(e)}")
    
    def export_data_to_csv(self, part_name, material_name, weight):
        """导出数据为CSV"""
        try:
            data = self.get_absorption_data(part_name, material_name, weight)
            
            if not data:
                raise Exception("未找到数据")
            
            # 创建CSV内容
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题信息
            basic_info = data['basic_info']
            writer.writerow(['垂直入射法吸音系数数据'])
            writer.writerow(['零件名称', basic_info['part_name']])
            writer.writerow(['材料名称', basic_info['material_name']])
            writer.writerow(['材料厂家', basic_info['material_manufacturer'] or ''])
            writer.writerow(['测试机构', basic_info['test_institution'] or ''])
            writer.writerow(['厚度(mm)', basic_info['thickness'] or ''])
            writer.writerow(['克重(g/m²)', basic_info['weight']])
            writer.writerow(['测试日期', basic_info['test_date']])
            writer.writerow(['测试工程师', basic_info['test_engineer'] or ''])
            writer.writerow(['测试地点', basic_info['test_location'] or ''])
            writer.writerow([])  # 空行
            
            # 写入数据表头
            writer.writerow(['频率', '测试值', '目标值', '达标状态'])
            
            # 写入数据
            for row in data['table_data']:
                status = '达标' if row['status'] == 'success' else '不达标' if row['status'] == 'danger' else ''
                writer.writerow([
                    row['frequency'],
                    row['test_value'] if row['test_value'] is not None else '',
                    row['target_value'] if row['target_value'] is not None else '',
                    status
                ])
            
            csv_content = output.getvalue()
            output.close()
            
            return csv_content
            
        except Exception as e:
            raise Exception(f"导出数据失败: {str(e)}")
    
    def export_comparison_data_to_csv(self, part_name, material_name, weights):
        """导出多克重对比数据为CSV"""
        try:
            data = self.get_multi_weight_comparison(part_name, material_name, weights)
            
            if not data:
                raise Exception("未找到数据")
            
            # 创建CSV内容
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入标题信息
            writer.writerow(['垂直入射法吸音系数多克重对比数据'])
            writer.writerow(['零件名称', part_name])
            writer.writerow(['材料名称', material_name])
            writer.writerow(['对比克重', ', '.join([f"{w}g/m²" for w in weights])])
            writer.writerow([])  # 空行
            
            # 构建表头
            header = ['频率']
            for weight in weights:
                header.extend([f'{weight}g/m²测试值', f'{weight}g/m²目标值'])
            writer.writerow(header)
            
            # 写入数据
            for row in data['table_data']:
                csv_row = [row['frequency']]
                for weight in weights:
                    test_value = row.get(f'test_{weight}')
                    target_value = row.get(f'target_{weight}')
                    csv_row.extend([
                        test_value if test_value is not None else '',
                        target_value if target_value is not None else ''
                    ])
                writer.writerow(csv_row)
            
            csv_content = output.getvalue()
            output.close()
            
            return csv_content
            
        except Exception as e:
            raise Exception(f"导出对比数据失败: {str(e)}")
