from models import db, VehicleModel, AirtightnessTestModel, AirtightnessImageModel

class AirtightnessService:
    """气密性业务逻辑服务"""
    
    def get_vehicle_list(self):
        """获取有气密性测试数据的车型列表"""
        # 查询有气密性测试数据的车型
        vehicles = db.session.query(VehicleModel)\
            .join(AirtightnessTestModel, VehicleModel.id == AirtightnessTestModel.vehicle_model_id)\
            .filter(VehicleModel.status == 'active')\
            .distinct().all()
        
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def get_all_vehicles(self):
        """获取所有车型列表（用于图片查看）"""
        vehicles = VehicleModel.query.filter_by(status='active').all()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def generate_comparison_data(self, vehicle_ids):
        """生成泄漏量对比数据"""
        if not vehicle_ids:
            return {'vehicles': [], 'areas': []}
        
        # 查询选中车型的测试数据
        tests = AirtightnessTestModel.query.filter(
            AirtightnessTestModel.vehicle_model_id.in_(vehicle_ids)
        ).all()
        
        # 查询车型信息
        vehicles = VehicleModel.query.filter(VehicleModel.id.in_(vehicle_ids)).all()
        vehicle_dict = {v.id: v for v in vehicles}
        
        # 组织车型数据
        vehicle_data = []
        test_dict = {}
        
        for test in tests:
            test_dict[test.vehicle_model_id] = test
        
        for vehicle_id in vehicle_ids:
            if vehicle_id in vehicle_dict:
                vehicle = vehicle_dict[vehicle_id]
                test = test_dict.get(vehicle_id)
                vehicle_data.append({
                    'id': vehicle.id,
                    'name': vehicle.vehicle_model_name,
                    'code': vehicle.vehicle_model_code,
                    'test_data': test.get_leakage_data() if test else None,
                    'test_info': {
                        'test_date': test.test_date.strftime('%Y-%m-%d') if test and test.test_date else None,
                        'test_engineer': test.test_engineer if test else None,
                        'test_location': test.test_location if test else None
                    } if test else None
                })
        
        # 获取区域配置
        area_config = AirtightnessTestModel.get_area_config()
        
        # 组织区域数据
        areas_data = []
        for category, items in area_config.items():
            area_item = {
                'category': category,
                'items': []
            }
            
            for item in items:
                item_data = {
                    'name': item['name'],
                    'field': item['field'],
                    'values': []
                }
                
                # 为每个车型获取该项的数值
                for vehicle in vehicle_data:
                    if vehicle['test_data']:
                        value = vehicle['test_data'].get(item['field'])
                        item_data['values'].append(value)
                    else:
                        item_data['values'].append(None)
                
                area_item['items'].append(item_data)
            
            areas_data.append(area_item)
        
        return {
            'vehicles': vehicle_data,
            'areas': areas_data
        }
    
    def get_vehicle_images(self, vehicle_id):
        """获取指定车型的测试图片"""
        images = AirtightnessImageModel.query.filter_by(vehicle_model_id=vehicle_id).first()
        vehicle = VehicleModel.query.get(vehicle_id)
        
        if not vehicle:
            return None
        
        return {
            'vehicle': {
                'id': vehicle.id,
                'name': vehicle.vehicle_model_name,
                'code': vehicle.vehicle_model_code
            },
            'images': images.get_images_dict() if images else {
                'front_compartment': None,
                'doors': None,
                'tailgate': None
            }
        }
    
    def export_comparison_data(self, vehicle_ids):
        """导出对比数据为CSV格式"""
        comparison_data = self.generate_comparison_data(vehicle_ids)
        
        if not comparison_data['vehicles']:
            return None
        
        # 构建CSV数据
        csv_data = []
        
        # 表头
        header = ['区域', '泄漏量（SCFM）']
        for vehicle in comparison_data['vehicles']:
            header.append(vehicle['name'])
        csv_data.append(header)
        
        # 数据行
        for area in comparison_data['areas']:
            if area['category'] == '整车不可控泄漏量':
                # 整车不可控泄漏量单独一行
                row = [area['category'], '']
                for item in area['items']:
                    for value in item['values']:
                        row.append(str(value) if value is not None else '')
                csv_data.append(row)
            else:
                # 其他区域有子项
                for i, item in enumerate(area['items']):
                    if i == 0:
                        # 第一行显示分类名称
                        row = [area['category'], item['name']]
                    else:
                        # 后续行分类名称为空
                        row = ['', item['name']]
                    
                    for value in item['values']:
                        row.append(str(value) if value is not None else '')
                    csv_data.append(row)
        
        return csv_data
