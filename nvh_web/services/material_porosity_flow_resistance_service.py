from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel, 
    MaterialModel, 
    MaterialPorosityFlowResistanceModel
)
import csv
import io


class MaterialPorosityFlowResistanceService:
    """材料孔隙率流阻业务逻辑服务"""
    
    def get_part_list(self):
        """获取有孔隙率流阻数据的零件列表"""
        parts = MaterialPorosityFlowResistanceModel.get_parts_with_data()
        return [{'name': part.part_name, 'description': part.description} for part in parts]
    
    def get_material_list(self, part_names=None):
        """获取有孔隙率流阻数据的材料列表"""
        materials = MaterialPorosityFlowResistanceModel.get_materials_with_data(part_names)
        
        # 按材料名称分组，每个材料名称只返回一次
        material_dict = {}
        for material in materials:
            if material.material_name not in material_dict:
                material_dict[material.material_name] = {
                    'name': material.material_name,
                    'description': material.description
                }
        
        return list(material_dict.values())
    
    def query_data(self, part_names=None, material_names=None):
        """查询孔隙率流阻数据"""
        try:
            data_list = MaterialPorosityFlowResistanceModel.get_data_by_conditions(
                part_names=part_names, 
                material_names=material_names
            )
            
            if not data_list:
                return []
            
            result = []
            for data in data_list:
                result.append(data.to_dict_with_related_data())
            
            return result
            
        except Exception as e:
            raise Exception(f"查询孔隙率流阻数据失败: {str(e)}")
    
    def export_data_to_csv(self, part_names=None, material_names=None):
        """导出孔隙率流阻数据为CSV格式"""
        try:
            data_list = self.query_data(part_names, material_names)
            
            if not data_list:
                raise Exception("没有找到匹配的数据")
            
            # 创建CSV内容
            output = io.StringIO()
            writer = csv.writer(output)
            
            # 写入表头
            headers = [
                '零件名称', '材料组成', '厚度(mm)', '克重(g/m²)', 
                '密度(kg/m³)', '孔隙率(%)', '孔隙率偏差(%)', 
                '流阻率(Pa·s/m²)', '流阻率偏差(Pa·s/m²)',
                '测试机构', '测试日期', '备注'
            ]
            writer.writerow(headers)
            
            # 写入数据行
            for data in data_list:
                row = [
                    data.get('part_name', ''),
                    data.get('material_name', ''),
                    data.get('thickness', ''),
                    data.get('weight', ''),
                    data.get('density', ''),
                    data.get('porosity', ''),
                    data.get('porosity_deviation', ''),
                    data.get('flow_resistance', ''),
                    data.get('flow_resistance_deviation', ''),
                    data.get('test_institution', ''),
                    data.get('test_date', ''),
                    data.get('remarks', '')
                ]
                writer.writerow(row)
            
            return output.getvalue()
            
        except Exception as e:
            raise Exception(f"导出CSV数据失败: {str(e)}")
    
    def get_statistics(self):
        """获取统计信息"""
        try:
            # 总数据条数
            total_count = MaterialPorosityFlowResistanceModel.query.count()
            
            # 零件数量
            part_count = db.session.query(SoundInsulationPartModel).join(
                MaterialPorosityFlowResistanceModel,
                SoundInsulationPartModel.id == MaterialPorosityFlowResistanceModel.part_id
            ).distinct().count()
            
            # 材料数量
            material_count = db.session.query(MaterialModel).join(
                MaterialPorosityFlowResistanceModel,
                MaterialModel.id == MaterialPorosityFlowResistanceModel.material_id
            ).distinct().count()
            
            return {
                'total_count': total_count,
                'part_count': part_count,
                'material_count': material_count
            }
            
        except Exception as e:
            raise Exception(f"获取统计信息失败: {str(e)}")


# 创建服务实例
material_porosity_flow_resistance_service = MaterialPorosityFlowResistanceService()
