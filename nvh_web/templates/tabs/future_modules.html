<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">待开发模块</h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            以下模块正在规划和开发中，将陆续上线
        </div>
    </div>
</div>

<div class="row">
    <!-- 声品质模块 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-volume-up me-2"></i>声品质模块
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">声品质评价和分析功能</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>响度分析</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>尖锐度分析</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>粗糙度分析</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>波动强度分析</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 振动模块 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>振动模块
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">振动数据管理和分析功能</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>振动测试数据</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>频谱分析</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>传递函数</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>相干函数</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计模块 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>数据统计模块
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">系统数据统计和报告生成</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>数据概览</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>趋势分析</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>报告生成</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>数据导出</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 经验数据库 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-book me-2"></i>经验数据库
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">NVH工程经验知识库</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>案例库</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>解决方案</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>最佳实践</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>知识搜索</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端应用 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>移动端应用
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">移动设备数据查看和管理</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>响应式设计</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>离线查看</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>数据同步</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>推送通知</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>

    <!-- API接口 -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-code me-2"></i>API接口
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">开放API接口服务</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success me-2"></i>RESTful API</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>接口文档</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>SDK支持</li>
                    <li><i class="fas fa-check-circle text-success me-2"></i>第三方集成</li>
                </ul>
                <div class="mt-3">
                    <span class="badge bg-warning">规划中</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-calendar-alt me-2"></i>开发路线图
                </h5>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6>第一阶段 - 已完成</h6>
                            <p class="text-muted">基础架构、模态数据、气密性测试、吸隔声模块</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>第二阶段 - 进行中</h6>
                            <p class="text-muted">多标签页界面、权限管理、数据统计</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6>第三阶段 - 规划中</h6>
                            <p class="text-muted">声品质模块、振动模块、经验数据库</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6>第四阶段 - 未来</h6>
                            <p class="text-muted">移动端应用、API接口、智能分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    padding-left: 1rem;
}

.timeline-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.timeline-content p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
</style>
