<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">业务中心</h1>
</div>

<!-- 搜索框 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="businessSearch" placeholder="搜索业务功能...">
            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 模态模块 -->
<div class="business-module mb-5">
    <h4 class="mb-3">
        <i class="fas fa-wave-square text-primary me-2"></i>模态模块
    </h4>
    <div class="row">
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-search fa-2x text-primary me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">模态数据查询</h6>
                            <small class="text-muted">Modal Data Query</small>
                        </div>
                    </div>
                    <p class="card-text">查询和分析车辆及零部件的模态测试数据，支持多维度筛选和数据导出</p>
                    <a href="{{ url_for('modal.search_page') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-chart-line fa-2x text-info me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">模态测试</h6>
                            <small class="text-muted">Modal Testing</small>
                        </div>
                    </div>
                    <p class="card-text">模态测试数据录入和管理功能</p>
                    <button class="btn btn-info btn-sm" onclick="showComingSoon()">
                        <i class="fas fa-clock me-1"></i>敬请期待
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 声学吸隔声模块 -->
<div class="business-module mb-5">
    <h4 class="mb-3">
        <i class="fas fa-volume-up text-success me-2"></i>声学吸隔声模块
    </h4>
    <div class="row">
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-search fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">垂直入射吸音系数查询</h6>
                            <small class="text-muted">Sound Absorption</small>
                        </div>
                    </div>
                    <p class="card-text">查询材料的垂直入射法吸音系数数据</p>
                    <a href="{{ url_for('sound_absorption.coefficient_query_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-shield-alt fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">垂直入射隔声量查询</h6>
                            <small class="text-muted">Sound Transmission</small>
                        </div>
                    </div>
                    <p class="card-text">查询材料的垂直入射法隔声量数据</p>
                    <a href="{{ url_for('sound_transmission.transmission_loss_query_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-wall-brick fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">上墙法隔声量查询</h6>
                            <small class="text-muted">Wall Mounted</small>
                        </div>
                    </div>
                    <p class="card-text">查询材料的上墙法隔声量数据</p>
                    <a href="{{ url_for('wall_mounted_transmission.transmission_loss_query_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-filter fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">孔隙率流阻查询</h6>
                            <small class="text-muted">Porosity & Flow Resistance</small>
                        </div>
                    </div>
                    <p class="card-text">查询材料的孔隙率和流阻数据</p>
                    <a href="{{ url_for('material_porosity_flow_resistance.query_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-chart-bar fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">区域隔声量对比</h6>
                            <small class="text-muted">Area Insulation</small>
                        </div>
                    </div>
                    <p class="card-text">车辆不同区域的隔声量对比分析</p>
                    <a href="{{ url_for('sound_insulation.area_comparison_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-car fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">车型隔声量对比</h6>
                            <small class="text-muted">Vehicle Insulation</small>
                        </div>
                    </div>
                    <p class="card-text">不同车型的隔声量对比分析</p>
                    <a href="{{ url_for('sound_insulation.vehicle_insulation_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-wave-square fa-2x text-success me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">车型混响时间对比</h6>
                            <small class="text-muted">Vehicle Reverberation</small>
                        </div>
                    </div>
                    <p class="card-text">不同车型的混响时间对比分析</p>
                    <a href="{{ url_for('sound_insulation.vehicle_reverberation_page') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 气密性测试模块 -->
<div class="business-module mb-5">
    <h4 class="mb-3">
        <i class="fas fa-tachometer-alt text-info me-2"></i>气密性测试模块
    </h4>
    <div class="row">
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-chart-bar fa-2x text-info me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">泄漏量对比</h6>
                            <small class="text-muted">Leakage Comparison</small>
                        </div>
                    </div>
                    <p class="card-text">车辆气密性泄漏量对比分析</p>
                    <a href="{{ url_for('airtightness.comparison_page') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card business-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-images fa-2x text-info me-3"></i>
                        <div>
                            <h6 class="card-title mb-1">测试图片</h6>
                            <small class="text-muted">Test Images</small>
                        </div>
                    </div>
                    <p class="card-text">气密性测试相关图片查看</p>
                    <a href="{{ url_for('airtightness.images_page') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>打开
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.business-module {
    border-left: 4px solid #e9ecef;
    padding-left: 1rem;
}

.business-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.business-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.business-card .card-body {
    padding: 1.25rem;
}

.business-card .card-title {
    font-size: 0.95rem;
    font-weight: 600;
}

.business-card .card-text {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

#businessSearch {
    border-radius: 0.375rem 0 0 0.375rem;
}

#searchBtn {
    border-radius: 0 0.375rem 0.375rem 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    const searchInput = document.getElementById('businessSearch');
    const searchBtn = document.getElementById('searchBtn');
    
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        const cards = document.querySelectorAll('.business-card');
        
        cards.forEach(card => {
            const title = card.querySelector('.card-title').textContent.toLowerCase();
            const text = card.querySelector('.card-text').textContent.toLowerCase();
            const small = card.querySelector('small').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || text.includes(searchTerm) || small.includes(searchTerm)) {
                card.closest('.col-md-6').style.display = 'block';
            } else {
                card.closest('.col-md-6').style.display = searchTerm ? 'none' : 'block';
            }
        });
    }
    
    searchInput.addEventListener('input', performSearch);
    searchBtn.addEventListener('click', performSearch);
    
    // 回车搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});
</script>
