<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">权限管理</h1>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-users-cog fa-4x text-muted mb-4"></i>
                <h3 class="mb-3">权限管理模块</h3>
                <p class="text-muted mb-4">该功能正在开发中，将提供以下功能：</p>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="feature-item">
                            <i class="fas fa-user-plus text-primary me-2"></i>
                            <span>用户管理</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="feature-item">
                            <i class="fas fa-users text-success me-2"></i>
                            <span>角色管理</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="feature-item">
                            <i class="fas fa-key text-warning me-2"></i>
                            <span>权限分配</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt text-info me-2"></i>
                            <span>访问控制</span>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    预计开发完成时间：敬请期待
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-item {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}
</style>
