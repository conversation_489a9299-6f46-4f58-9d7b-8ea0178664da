/**
 * 标签页样式
 */

/* 标签页栏容器 */
.tab-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
    margin: 0 -1.5rem 1rem -1.5rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* 标签页列表 */
.tab-list {
    display: flex;
    align-items: center;
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-list::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 标签页项目 */
.tab-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-bottom: none;
    margin-right: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
}

.tab-item:first-child {
    border-top-left-radius: 0.375rem;
}

.tab-item:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.tab-item.active {
    background-color: #ffffff;
    border-color: #0d6efd;
    border-bottom: 2px solid #0d6efd;
    color: #0d6efd;
    font-weight: 500;
}

/* 标签页图标 */
.tab-icon {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

/* 标签页标题 */
.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
}

/* 标签页关闭按钮 */
.tab-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.75rem;
    padding: 0.25rem;
    margin-left: 0.5rem;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.tab-close:hover {
    background-color: #dc3545;
    color: #ffffff;
    opacity: 1;
}

.tab-item:not(:hover) .tab-close {
    opacity: 0.5;
}

/* 标签页操作按钮 */
.tab-actions {
    padding: 0.5rem;
    border-left: 1px solid #dee2e6;
}

.tab-actions .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
}

/* 标签页内容容器 */
.tab-content-container {
    position: relative;
    min-height: 400px;
}

/* 标签页面板 */
.tab-pane {
    display: none;
    animation: fadeIn 0.2s ease-in-out;
}

.tab-pane.active {
    display: block;
}

/* 标签页错误状态 */
.tab-error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: #6c757d;
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-bar {
        margin: 0 -1rem 1rem -1rem;
    }
    
    .tab-item {
        min-width: 100px;
        max-width: 150px;
        padding: 0.5rem 0.75rem;
    }
    
    .tab-title {
        font-size: 0.8rem;
    }
    
    .tab-close {
        width: 1.25rem;
        height: 1.25rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .tab-item {
        min-width: 80px;
        max-width: 120px;
        padding: 0.5rem;
    }
    
    .tab-icon {
        margin-right: 0.25rem;
    }
    
    .tab-title {
        font-size: 0.75rem;
    }
}

/* 滚动条样式优化 */
.tab-list {
    scrollbar-width: thin;
    scrollbar-color: #adb5bd transparent;
}

.tab-list::-webkit-scrollbar {
    height: 4px;
}

.tab-list::-webkit-scrollbar-track {
    background: transparent;
}

.tab-list::-webkit-scrollbar-thumb {
    background-color: #adb5bd;
    border-radius: 2px;
}

.tab-list::-webkit-scrollbar-thumb:hover {
    background-color: #6c757d;
}

/* 标签页拖拽效果（预留） */
.tab-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.tab-item.drag-over {
    border-left: 3px solid #0d6efd;
}

/* 新标签页按钮（预留） */
.new-tab-btn {
    background: none;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 0.5rem;
    margin-left: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.new-tab-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

/* 标签页加载状态 */
.tab-item.loading .tab-icon::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.25rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 标签页数量提示 */
.tab-counter {
    background-color: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.75rem;
    margin-left: 0.5rem;
}

/* 标签页右键菜单（预留） */
.tab-context-menu {
    position: absolute;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 150px;
}

.tab-context-menu .menu-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.tab-context-menu .menu-item:hover {
    background-color: #f8f9fa;
}

.tab-context-menu .menu-item:last-child {
    border-bottom: none;
}

.tab-context-menu .menu-item.disabled {
    color: #6c757d;
    cursor: not-allowed;
}

.tab-context-menu .menu-item.disabled:hover {
    background-color: transparent;
}
