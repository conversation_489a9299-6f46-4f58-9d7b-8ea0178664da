/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 76px; /* 导航栏高度 + 额外间距 */
}

/* 导航栏固定样式 */
.navbar.fixed-top {
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1030; /* Bootstrap导航栏标准z-index */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #0d6efd !important; /* 确保背景色不透明 */
}

/* 防止导航栏内容换行 */
.navbar .container-fluid {
    flex-wrap: nowrap;
}

/* 确保导航栏高度一致 */
.navbar {
    min-height: 56px;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 76px; /* 与body的padding-top保持一致 */
    bottom: 0;
    left: 0;
    z-index: 1020; /* 低于导航栏但高于内容 */
    padding: 0;
    background-color: #2c3e50 !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    transition: transform 0.3s ease;
    width: 16.66667%; /* col-lg-2 width */
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

/* 侧边栏切换按钮样式 */
#sidebarToggle {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.sidebar .nav-link {
    color: #ecf0f1;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.sidebar .nav-link:hover {
    background-color: #34495e;
    color: #3498db;
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: #ffffff;
    border-left: 4px solid #2980b9;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* 侧边栏展开/收起图标 */
.sidebar .nav-link[data-bs-toggle="collapse"] {
    position: relative;
}

.sidebar .nav-link[data-bs-toggle="collapse"]::after {
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 1rem;
    transition: transform 0.3s ease;
}

.sidebar .nav-link[data-bs-toggle="collapse"].collapsed::after {
    transform: rotate(-90deg);
}

.sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    transition: transform 0.3s ease;
    margin-left: auto !important;
}

.sidebar .nav-link[data-bs-toggle="collapse"]:not(.collapsed) .fa-chevron-down {
    transform: rotate(180deg);
}

/* 子菜单样式 */
.sidebar .collapse .nav-link {
    color: #bdc3c7;
    background-color: rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
}

.sidebar .collapse .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.2);
    color: #3498db;
    border-left-color: #3498db;
}

/* 主内容区域 */
main {
    padding-top: 20px;
    min-height: calc(100vh - 76px); /* 减去导航栏高度 */
    background-color: #ffffff;
    transition: margin-left 0.3s ease, width 0.3s ease;
    position: relative;
    z-index: 1;
}

/* 侧边栏收起时主内容区域调整 */
.sidebar-collapsed main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* 确保内容区域平滑过渡 */
.container-fluid .row {
    transition: all 0.3s ease;
}

/* 侧边栏收起时调整容器 */
.sidebar-collapsed .container-fluid .row {
    margin-left: 0;
}

/* 卡片样式 */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
    border-radius: 0.5rem;
}

.card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* 确保导航栏下拉菜单的z-index正确 */
.navbar .dropdown-menu {
    z-index: 1040;
}

/* 导航栏按钮样式 */
.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5);
}

.navbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.75);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    body {
        padding-top: 66px; /* 移动设备上稍微减少间距 */
    }

    .navbar.fixed-top {
        position: fixed !important; /* 确保在移动设备上也固定 */
    }

    .sidebar {
        position: relative;
        top: 0;
        height: auto;
        width: 100%;
        transform: none;
        z-index: 1015; /* 在移动设备上调整层级 */
    }

    .sidebar.collapsed {
        display: none;
    }

    main {
        padding-top: 10px;
        min-height: calc(100vh - 66px); /* 调整移动设备的最小高度 */
    }

    .sidebar-collapsed main {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 768px) {
    .sidebar {
        width: 25%; /* col-md-3 width */
    }
}

@media (min-width: 992px) {
    .sidebar {
        width: 16.66667%; /* col-lg-2 width */
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

.toast {
    min-width: 300px;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
}

/* 分页样式 */
.pagination .page-link {
    color: #007bff;
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 列表组样式 */
.list-group-item {
    border-color: #e9ecef;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}
