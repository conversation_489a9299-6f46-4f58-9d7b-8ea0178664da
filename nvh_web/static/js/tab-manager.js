/**
 * 标签页管理器
 * 负责管理多标签页界面的创建、切换、关闭等功能
 */
class TabManager {
    constructor() {
        this.tabs = new Map(); // 存储标签页信息
        this.activeTabId = null; // 当前激活的标签页ID
        this.tabCounter = 0; // 标签页计数器
        this.maxTabs = 10; // 最大标签页数量
        
        this.init();
    }

    /**
     * 初始化标签页管理器
     */
    init() {
        this.createTabContainer();
        this.bindEvents();
        
        // 根据当前URL打开对应标签页
        this.openTabFromCurrentUrl();
    }

    /**
     * 创建标签页容器
     */
    createTabContainer() {
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) return;

        // 创建标签页栏
        const tabBar = document.createElement('div');
        tabBar.id = 'tab-bar';
        tabBar.className = 'tab-bar';
        tabBar.innerHTML = `
            <div class="tab-list" id="tab-list">
                <!-- 标签页将在这里动态生成 -->
            </div>
            <div class="tab-actions">
                <button class="btn btn-sm btn-outline-secondary" id="close-all-tabs" title="关闭所有标签页">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 创建标签页内容容器
        const tabContent = document.createElement('div');
        tabContent.id = 'tab-content';
        tabContent.className = 'tab-content-container';

        // 插入到主内容区域
        mainContent.insertBefore(tabBar, mainContent.firstChild);
        mainContent.insertBefore(tabContent, tabBar.nextSibling);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭所有标签页按钮
        const closeAllBtn = document.getElementById('close-all-tabs');
        if (closeAllBtn) {
            closeAllBtn.addEventListener('click', () => this.closeAllTabs());
        }

        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.tabId) {
                this.switchToTab(e.state.tabId);
            }
        });

        // 监听侧边栏菜单点击
        this.bindSidebarEvents();
    }

    /**
     * 绑定侧边栏事件
     */
    bindSidebarEvents() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        sidebar.addEventListener('click', (e) => {
            const link = e.target.closest('a.nav-link');
            if (!link || link.getAttribute('data-bs-toggle')) return;

            e.preventDefault();
            
            const href = link.getAttribute('href');
            const title = link.textContent.trim();
            const icon = link.querySelector('i') ? link.querySelector('i').className : 'fas fa-file';

            if (href && href !== '#' && !href.includes('javascript:')) {
                this.openTab({
                    url: href,
                    title: title,
                    icon: icon
                });
            }
        });
    }

    /**
     * 根据当前URL打开标签页
     */
    openTabFromCurrentUrl() {
        const path = window.location.pathname;
        let title = '首页';
        let icon = 'fas fa-home';

        // 根据路径确定标题和图标
        if (path === '/' || path === '/home') {
            title = '首页';
            icon = 'fas fa-home';
        } else if (path === '/business_center') {
            title = '业务中心';
            icon = 'fas fa-briefcase';
        }

        this.openTab({
            url: path === '/' ? '/home' : path,
            title: title,
            icon: icon
        });
    }

    /**
     * 打开新标签页
     * @param {Object} options - 标签页选项
     * @param {string} options.url - 页面URL
     * @param {string} options.title - 标签页标题
     * @param {string} options.icon - 标签页图标
     * @param {boolean} options.closable - 是否可关闭
     */
    openTab(options) {
        const { url, title, icon = 'fas fa-file', closable = true } = options;
        
        // 检查是否已存在相同URL的标签页
        const existingTab = this.findTabByUrl(url);
        if (existingTab) {
            this.switchToTab(existingTab.id);
            return;
        }

        // 检查标签页数量限制
        if (this.tabs.size >= this.maxTabs) {
            this.showMessage('标签页数量已达上限，请关闭一些标签页后再试', 'warning');
            return;
        }

        const tabId = `tab-${++this.tabCounter}`;
        const tab = {
            id: tabId,
            url: url,
            title: title,
            icon: icon,
            closable: closable,
            loaded: false,
            content: null
        };

        this.tabs.set(tabId, tab);
        this.createTabElement(tab);
        this.loadTabContent(tab);
        this.switchToTab(tabId);
        
        // 更新URL
        this.updateUrl(url, tabId);
    }

    /**
     * 创建标签页元素
     * @param {Object} tab - 标签页对象
     */
    createTabElement(tab) {
        const tabList = document.getElementById('tab-list');
        if (!tabList) return;

        const tabElement = document.createElement('div');
        tabElement.className = 'tab-item';
        tabElement.setAttribute('data-tab-id', tab.id);
        
        tabElement.innerHTML = `
            <span class="tab-icon"><i class="${tab.icon}"></i></span>
            <span class="tab-title">${tab.title}</span>
            ${tab.closable ? '<button class="tab-close" title="关闭标签页"><i class="fas fa-times"></i></button>' : ''}
        `;

        // 绑定点击事件
        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                this.switchToTab(tab.id);
            }
        });

        // 绑定关闭事件
        if (tab.closable) {
            const closeBtn = tabElement.querySelector('.tab-close');
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.closeTab(tab.id);
            });
        }

        tabList.appendChild(tabElement);
    }

    /**
     * 加载标签页内容
     * @param {Object} tab - 标签页对象
     */
    async loadTabContent(tab) {
        try {
            const response = await fetch(tab.url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const html = await response.text();
            
            // 创建内容容器
            const contentDiv = document.createElement('div');
            contentDiv.className = 'tab-pane';
            contentDiv.setAttribute('data-tab-id', tab.id);
            contentDiv.innerHTML = html;
            
            // 添加到内容容器
            const tabContent = document.getElementById('tab-content');
            if (tabContent) {
                tabContent.appendChild(contentDiv);
            }
            
            tab.loaded = true;
            tab.content = contentDiv;
            
        } catch (error) {
            console.error('加载标签页内容失败:', error);
            this.showTabError(tab, '加载页面失败');
        }
    }

    /**
     * 显示标签页错误
     * @param {Object} tab - 标签页对象
     * @param {string} message - 错误消息
     */
    showTabError(tab, message) {
        const contentDiv = document.createElement('div');
        contentDiv.className = 'tab-pane tab-error';
        contentDiv.setAttribute('data-tab-id', tab.id);
        contentDiv.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>${message}</h5>
                <button class="btn btn-primary mt-3" onclick="tabManager.reloadTab('${tab.id}')">
                    <i class="fas fa-redo me-2"></i>重新加载
                </button>
            </div>
        `;
        
        const tabContent = document.getElementById('tab-content');
        if (tabContent) {
            tabContent.appendChild(contentDiv);
        }
        
        tab.content = contentDiv;
    }

    /**
     * 切换到指定标签页
     * @param {string} tabId - 标签页ID
     */
    switchToTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        // 更新标签页状态
        this.updateTabStates(tabId);
        
        // 更新内容显示
        this.updateContentDisplay(tabId);
        
        this.activeTabId = tabId;
        
        // 更新URL
        this.updateUrl(tab.url, tabId);
    }

    /**
     * 更新标签页状态
     * @param {string} activeTabId - 激活的标签页ID
     */
    updateTabStates(activeTabId) {
        const tabElements = document.querySelectorAll('.tab-item');
        tabElements.forEach(element => {
            const tabId = element.getAttribute('data-tab-id');
            if (tabId === activeTabId) {
                element.classList.add('active');
            } else {
                element.classList.remove('active');
            }
        });
    }

    /**
     * 更新内容显示
     * @param {string} activeTabId - 激活的标签页ID
     */
    updateContentDisplay(activeTabId) {
        const contentElements = document.querySelectorAll('.tab-pane');
        contentElements.forEach(element => {
            const tabId = element.getAttribute('data-tab-id');
            if (tabId === activeTabId) {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        });
    }

    /**
     * 关闭标签页
     * @param {string} tabId - 标签页ID
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || !tab.closable) return;

        // 移除DOM元素
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.remove();
        }

        // 移除内容
        if (tab.content) {
            tab.content.remove();
        }

        // 从Map中删除
        this.tabs.delete(tabId);

        // 如果关闭的是当前激活标签页，切换到其他标签页
        if (this.activeTabId === tabId) {
            const remainingTabs = Array.from(this.tabs.keys());
            if (remainingTabs.length > 0) {
                this.switchToTab(remainingTabs[remainingTabs.length - 1]);
            } else {
                // 没有标签页了，打开首页
                this.openTab({
                    url: '/home',
                    title: '首页',
                    icon: 'fas fa-home',
                    closable: false
                });
            }
        }
    }

    /**
     * 关闭所有标签页
     */
    closeAllTabs() {
        const tabIds = Array.from(this.tabs.keys());
        tabIds.forEach(tabId => {
            const tab = this.tabs.get(tabId);
            if (tab && tab.closable) {
                this.closeTab(tabId);
            }
        });
    }

    /**
     * 重新加载标签页
     * @param {string} tabId - 标签页ID
     */
    reloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        // 移除旧内容
        if (tab.content) {
            tab.content.remove();
        }

        tab.loaded = false;
        this.loadTabContent(tab);
    }

    /**
     * 根据URL查找标签页
     * @param {string} url - URL
     * @returns {Object|null} 标签页对象
     */
    findTabByUrl(url) {
        for (const tab of this.tabs.values()) {
            if (tab.url === url) {
                return tab;
            }
        }
        return null;
    }

    /**
     * 更新浏览器URL
     * @param {string} url - 页面URL
     * @param {string} tabId - 标签页ID
     */
    updateUrl(url, tabId) {
        const state = { tabId: tabId };
        history.pushState(state, '', url);
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 使用现有的消息显示功能
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 全局标签页管理器实例
let tabManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 只在有侧边栏的页面初始化标签页管理器
    if (document.getElementById('sidebar')) {
        tabManager = new TabManager();
        
        // 将实例挂载到全局对象，方便调试和外部调用
        window.tabManager = tabManager;
    }
});
