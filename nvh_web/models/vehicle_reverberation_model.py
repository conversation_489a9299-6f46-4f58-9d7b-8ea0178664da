from models.base_model import BaseModel, db
from sqlalchemy import and_

class VehicleReverberationModel(BaseModel):
    """车型混响时间数据模型"""
    __tablename__ = 'vehicle_reverberation_data'
    
    vehicle_model_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('vehicle_models.id'), nullable=False, comment='车型ID')
    
    # 中心频率数据 (400Hz-10000Hz)
    freq_400 = db.Column(db.Numeric(5, 3), comment='400Hz混响时间(s)')
    freq_500 = db.Column(db.Numeric(5, 3), comment='500Hz混响时间(s)')
    freq_630 = db.Column(db.Numeric(5, 3), comment='630Hz混响时间(s)')
    freq_800 = db.Column(db.Numeric(5, 3), comment='800Hz混响时间(s)')
    freq_1000 = db.Column(db.<PERSON>ume<PERSON>(5, 3), comment='1000Hz混响时间(s)')
    freq_1250 = db.Column(db.Numeric(5, 3), comment='1250Hz混响时间(s)')
    freq_1600 = db.Column(db.Numeric(5, 3), comment='1600Hz混响时间(s)')
    freq_2000 = db.Column(db.Numeric(5, 3), comment='2000Hz混响时间(s)')
    freq_2500 = db.Column(db.Numeric(5, 3), comment='2500Hz混响时间(s)')
    freq_3150 = db.Column(db.Numeric(5, 3), comment='3150Hz混响时间(s)')
    freq_4000 = db.Column(db.Numeric(5, 3), comment='4000Hz混响时间(s)')
    freq_5000 = db.Column(db.Numeric(5, 3), comment='5000Hz混响时间(s)')
    freq_6300 = db.Column(db.Numeric(5, 3), comment='6300Hz混响时间(s)')
    freq_8000 = db.Column(db.Numeric(5, 3), comment='8000Hz混响时间(s)')
    freq_10000 = db.Column(db.Numeric(5, 3), comment='10000Hz混响时间(s)')
    
    # 测试信息
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    remarks = db.Column(db.Text, comment='备注')
    
    # 关联关系
    vehicle = db.relationship('VehicleModel', backref='vehicle_reverberation_data')
    
    def __repr__(self):
        return f'<VehicleReverberationModel 车型{self.vehicle_model_id}>'
    
    @classmethod
    def get_frequency_columns(cls):
        """获取所有频率列名"""
        return [
            'freq_400', 'freq_500', 'freq_630',
            'freq_800', 'freq_1000', 'freq_1250', 'freq_1600', 'freq_2000', 'freq_2500',
            'freq_3150', 'freq_4000', 'freq_5000', 'freq_6300', 'freq_8000', 'freq_10000'
        ]
    
    @classmethod
    def get_frequency_labels(cls):
        """获取频率标签"""
        return [
            '400', '500', '630',
            '800', '1000', '1250', '1600', '2000', '2500',
            '3150', '4000', '5000', '6300', '8000', '10000'
        ]
    
    @classmethod
    def get_vehicles_with_data(cls):
        """获取有数据的车型列表"""
        from models import VehicleModel
        return db.session.query(VehicleModel).join(
            cls, VehicleModel.id == cls.vehicle_model_id
        ).filter(VehicleModel.status == 'active').distinct().all()
    
    @classmethod
    def get_comparison_data(cls, vehicle_ids):
        """获取多个车型的对比数据"""
        return cls.query.filter(cls.vehicle_model_id.in_(vehicle_ids)).all()
    
    @classmethod
    def get_by_vehicle_id(cls, vehicle_id):
        """根据车型ID获取数据"""
        return cls.query.filter_by(vehicle_model_id=vehicle_id).first()
    
    def get_frequency_data(self):
        """获取频率数据字典"""
        freq_columns = self.get_frequency_columns()
        freq_labels = self.get_frequency_labels()
        
        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None
        
        return data
    
    def to_dict_with_frequency_data(self):
        """转换为包含频率数据的字典"""
        result = self.to_dict()
        result['frequency_data'] = self.get_frequency_data()
        return result
