from models.base_model import BaseModel, db
from sqlalchemy import and_

class SoundInsulationDataModel(BaseModel):
    """吸隔声数据模型"""
    __tablename__ = 'sound_insulation_data'
    
    vehicle_model_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('vehicle_models.id'), nullable=False, comment='车型ID')
    area_id = db.Column(db.Integer, db.Foreign<PERSON>ey('sound_insulation_areas.id'), nullable=False, comment='区域ID')
    
    # 中心频率数据 (200Hz-10000Hz)
    freq_200 = db.Column(db.Numeric(5, 2), comment='200Hz隔声量(dB)')
    freq_250 = db.Column(db.Numeric(5, 2), comment='250Hz隔声量(dB)')
    freq_315 = db.Column(db.Numeric(5, 2), comment='315Hz隔声量(dB)')
    freq_400 = db.Column(db.Numeric(5, 2), comment='400Hz隔声量(dB)')
    freq_500 = db.Column(db.Numeric(5, 2), comment='500Hz隔声量(dB)')
    freq_630 = db.Column(db.Numeric(5, 2), comment='630Hz隔声量(dB)')
    freq_800 = db.Column(db.Numeric(5, 2), comment='800Hz隔声量(dB)')
    freq_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz隔声量(dB)')
    freq_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz隔声量(dB)')
    freq_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz隔声量(dB)')
    freq_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz隔声量(dB)')
    freq_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz隔声量(dB)')
    freq_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz隔声量(dB)')
    freq_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz隔声量(dB)')
    freq_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz隔声量(dB)')
    freq_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz隔声量(dB)')
    freq_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz隔声量(dB)')
    freq_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz隔声量(dB)')
    
    # 测试信息
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    remarks = db.Column(db.Text, comment='备注')
    
    # 关联关系
    vehicle = db.relationship('VehicleModel', backref='sound_insulation_data')
    
    def __repr__(self):
        return f'<SoundInsulationDataModel 车型{self.vehicle_model_id} 区域{self.area_id}>'
    
    @classmethod
    def get_frequency_columns(cls):
        """获取所有频率列名"""
        return [
            'freq_200', 'freq_250', 'freq_315', 'freq_400', 'freq_500', 'freq_630',
            'freq_800', 'freq_1000', 'freq_1250', 'freq_1600', 'freq_2000', 'freq_2500',
            'freq_3150', 'freq_4000', 'freq_5000', 'freq_6300', 'freq_8000', 'freq_10000'
        ]
    
    @classmethod
    def get_frequency_labels(cls):
        """获取频率标签"""
        return [
            '200', '250', '315', '400', '500', '630',
            '800', '1000', '1250', '1600', '2000', '2500',
            '3150', '4000', '5000', '6300', '8000', '10000'
        ]
    
    @classmethod
    def get_data_by_area_and_vehicles(cls, area_id, vehicle_ids):
        """根据区域和车型获取数据"""
        return cls.query.filter(
            and_(
                cls.area_id == area_id,
                cls.vehicle_model_id.in_(vehicle_ids)
            )
        ).all()
    
    @classmethod
    def get_vehicles_with_data_by_area(cls, area_id):
        """获取指定区域有数据的车型列表"""
        from models.vehicle_model import VehicleModel
        return db.session.query(VehicleModel)\
            .join(cls, VehicleModel.id == cls.vehicle_model_id)\
            .filter(cls.area_id == area_id)\
            .filter(VehicleModel.status == 'active')\
            .distinct().all()
    
    def get_frequency_data(self):
        """获取频率数据字典"""
        freq_columns = self.get_frequency_columns()
        freq_labels = self.get_frequency_labels()
        
        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None
        
        return data
    
    def to_dict_with_frequency_data(self):
        """转换为包含频率数据的字典"""
        result = self.to_dict()
        result['frequency_data'] = self.get_frequency_data()
        return result
