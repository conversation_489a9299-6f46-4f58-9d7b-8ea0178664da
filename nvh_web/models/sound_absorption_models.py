from models.base_model import BaseModel, db
from sqlalchemy import and_

class SoundInsulationPartModel(BaseModel):
    """吸隔声零件模型"""
    __tablename__ = 'sound_insulation_parts'

    part_name = db.Column(db.String(100), nullable=False, unique=True, comment='零件名称')
    description = db.Column(db.Text, comment='零件描述')

    # 关系映射
    absorption_coefficients = db.relationship('SoundAbsorptionCoefficientModel', back_populates='part', lazy='dynamic')
    transmission_losses = db.relationship('SoundTransmissionLossModel', back_populates='part', lazy='dynamic')
    wall_mounted_transmission_losses = db.relationship('WallMountedTransmissionLossModel', lazy='dynamic')

    def __repr__(self):
        return f'<SoundInsulationPartModel {self.part_name}>'

    @classmethod
    def get_all_parts(cls):
        """获取所有零件"""
        return cls.query.order_by(cls.part_name).all()

    @classmethod
    def get_parts_with_data(cls):
        """获取有数据的零件列表"""
        return db.session.query(cls).join(
            SoundAbsorptionCoefficientModel,
            cls.id == SoundAbsorptionCoefficientModel.part_id
        ).distinct().order_by(cls.part_name).all()

    @classmethod
    def get_parts_with_transmission_data(cls):
        """获取有隔声量数据的零件列表"""
        return db.session.query(cls).join(
            SoundTransmissionLossModel,
            cls.id == SoundTransmissionLossModel.part_id
        ).distinct().order_by(cls.part_name).all()

    @classmethod
    def get_parts_with_wall_mounted_transmission_data(cls):
        """获取有上墙法隔声量数据的零件列表"""
        return db.session.query(cls).join(
            WallMountedTransmissionLossModel,
            cls.id == WallMountedTransmissionLossModel.part_id
        ).distinct().order_by(cls.part_name).all()


class MaterialModel(BaseModel):
    """材料模型"""
    __tablename__ = 'materials'

    material_name = db.Column(db.String(100), nullable=False, comment='材料名称')
    thickness = db.Column(db.Numeric(6, 2), nullable=False, comment='厚度(mm)')
    weight = db.Column(db.Numeric(8, 2), nullable=False, comment='克重(g/m²)')
    description = db.Column(db.Text, comment='材料描述')

    # 关系映射
    absorption_coefficients = db.relationship('SoundAbsorptionCoefficientModel', back_populates='material', lazy='dynamic')
    transmission_losses = db.relationship('SoundTransmissionLossModel', back_populates='material', lazy='dynamic')
    wall_mounted_transmission_losses = db.relationship('WallMountedTransmissionLossModel', lazy='dynamic')

    # 唯一约束：材料名称+厚度+克重的组合必须唯一
    __table_args__ = (
        db.UniqueConstraint('material_name', 'thickness', 'weight', name='uk_material_spec'),
    )

    def __repr__(self):
        return f'<MaterialModel {self.material_name}-{self.thickness}mm-{self.weight}g/m²>'

    @property
    def display_name(self):
        """显示名称：材料名称 (厚度mm, 克重g/m²)"""
        return f"{self.material_name} ({self.thickness}mm, {self.weight}g/m²)"

    @classmethod
    def get_all_materials(cls):
        """获取所有材料"""
        return cls.query.order_by(cls.material_name, cls.thickness, cls.weight).all()

    @classmethod
    def get_materials_by_part(cls, part_id):
        """根据零件ID获取材料列表"""
        return db.session.query(cls).join(
            SoundAbsorptionCoefficientModel,
            cls.id == SoundAbsorptionCoefficientModel.material_id
        ).filter(
            SoundAbsorptionCoefficientModel.part_id == part_id
        ).distinct().order_by(cls.material_name, cls.thickness, cls.weight).all()

    @classmethod
    def get_materials_by_part_name(cls, part_name):
        """根据零件名称获取材料列表（兼容旧接口）"""
        from models.sound_absorption_models import SoundInsulationPartModel
        part = SoundInsulationPartModel.query.filter_by(part_name=part_name).first()
        if part:
            return cls.get_materials_by_part(part.id)
        return []

    @classmethod
    def get_materials_by_part_name_for_transmission(cls, part_name):
        """根据零件名称获取隔声量相关的材料列表"""
        return db.session.query(cls).join(
            SoundTransmissionLossModel,
            cls.id == SoundTransmissionLossModel.material_id
        ).join(
            SoundInsulationPartModel,
            SoundInsulationPartModel.id == SoundTransmissionLossModel.part_id
        ).filter(
            SoundInsulationPartModel.part_name == part_name
        ).distinct().order_by(cls.material_name, cls.thickness, cls.weight).all()

    @classmethod
    def get_materials_by_part_name_for_wall_mounted_transmission(cls, part_name):
        """根据零件名称获取上墙法隔声量相关的材料列表"""
        return db.session.query(cls).join(
            WallMountedTransmissionLossModel,
            cls.id == WallMountedTransmissionLossModel.material_id
        ).join(
            SoundInsulationPartModel,
            SoundInsulationPartModel.id == WallMountedTransmissionLossModel.part_id
        ).filter(
            SoundInsulationPartModel.part_name == part_name
        ).distinct().order_by(cls.material_name, cls.thickness, cls.weight).all()


class MaterialManufacturerModel(BaseModel):
    """材料厂家模型"""
    __tablename__ = 'material_manufacturers'

    manufacturer_name = db.Column(db.String(100), nullable=False, unique=True, comment='厂家名称')
    description = db.Column(db.Text, comment='厂家描述')

    # 关系映射
    absorption_coefficients = db.relationship('SoundAbsorptionCoefficientModel', back_populates='manufacturer', lazy='dynamic')
    transmission_losses = db.relationship('SoundTransmissionLossModel', back_populates='manufacturer', lazy='dynamic')
    wall_mounted_transmission_losses = db.relationship('WallMountedTransmissionLossModel', lazy='dynamic')

    def __repr__(self):
        return f'<MaterialManufacturerModel {self.manufacturer_name}>'

    @classmethod
    def get_all_manufacturers(cls):
        """获取所有厂家"""
        return cls.query.order_by(cls.manufacturer_name).all()


class SoundAbsorptionCoefficientModel(BaseModel):
    """吸声系数模型"""
    __tablename__ = 'sound_absorption_coefficients'

    # 外键字段
    part_id = db.Column(db.Integer, db.ForeignKey('sound_insulation_parts.id'), nullable=False, comment='零件ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='材料ID')
    manufacturer_id = db.Column(db.Integer, db.ForeignKey('material_manufacturers.id'), comment='厂家ID')
    test_institution = db.Column(db.String(100), comment='测试机构')

    # 关系映射
    part = db.relationship('SoundInsulationPartModel', back_populates='absorption_coefficients', foreign_keys=[part_id])
    material = db.relationship('MaterialModel', back_populates='absorption_coefficients', foreign_keys=[material_id])
    manufacturer = db.relationship('MaterialManufacturerModel', back_populates='absorption_coefficients', foreign_keys=[manufacturer_id])
    
    # 测试值 (19个频率点)
    test_value_125 = db.Column(db.Numeric(4, 3), comment='125Hz测试值')
    test_value_160 = db.Column(db.Numeric(4, 3), comment='160Hz测试值')
    test_value_200 = db.Column(db.Numeric(4, 3), comment='200Hz测试值')
    test_value_250 = db.Column(db.Numeric(4, 3), comment='250Hz测试值')
    test_value_315 = db.Column(db.Numeric(4, 3), comment='315Hz测试值')
    test_value_400 = db.Column(db.Numeric(4, 3), comment='400Hz测试值')
    test_value_500 = db.Column(db.Numeric(4, 3), comment='500Hz测试值')
    test_value_630 = db.Column(db.Numeric(4, 3), comment='630Hz测试值')
    test_value_800 = db.Column(db.Numeric(4, 3), comment='800Hz测试值')
    test_value_1000 = db.Column(db.Numeric(4, 3), comment='1000Hz测试值')
    test_value_1250 = db.Column(db.Numeric(4, 3), comment='1250Hz测试值')
    test_value_1600 = db.Column(db.Numeric(4, 3), comment='1600Hz测试值')
    test_value_2000 = db.Column(db.Numeric(4, 3), comment='2000Hz测试值')
    test_value_2500 = db.Column(db.Numeric(4, 3), comment='2500Hz测试值')
    test_value_3150 = db.Column(db.Numeric(4, 3), comment='3150Hz测试值')
    test_value_4000 = db.Column(db.Numeric(4, 3), comment='4000Hz测试值')
    test_value_5000 = db.Column(db.Numeric(4, 3), comment='5000Hz测试值')
    test_value_6300 = db.Column(db.Numeric(4, 3), comment='6300Hz测试值')
    test_value_8000 = db.Column(db.Numeric(4, 3), comment='8000Hz测试值')
    test_value_10000 = db.Column(db.Numeric(4, 3), comment='10000Hz测试值')
    
    # 目标值 (19个频率点)
    target_value_125 = db.Column(db.Numeric(4, 3), comment='125Hz目标值')
    target_value_160 = db.Column(db.Numeric(4, 3), comment='160Hz目标值')
    target_value_200 = db.Column(db.Numeric(4, 3), comment='200Hz目标值')
    target_value_250 = db.Column(db.Numeric(4, 3), comment='250Hz目标值')
    target_value_315 = db.Column(db.Numeric(4, 3), comment='315Hz目标值')
    target_value_400 = db.Column(db.Numeric(4, 3), comment='400Hz目标值')
    target_value_500 = db.Column(db.Numeric(4, 3), comment='500Hz目标值')
    target_value_630 = db.Column(db.Numeric(4, 3), comment='630Hz目标值')
    target_value_800 = db.Column(db.Numeric(4, 3), comment='800Hz目标值')
    target_value_1000 = db.Column(db.Numeric(4, 3), comment='1000Hz目标值')
    target_value_1250 = db.Column(db.Numeric(4, 3), comment='1250Hz目标值')
    target_value_1600 = db.Column(db.Numeric(4, 3), comment='1600Hz目标值')
    target_value_2000 = db.Column(db.Numeric(4, 3), comment='2000Hz目标值')
    target_value_2500 = db.Column(db.Numeric(4, 3), comment='2500Hz目标值')
    target_value_3150 = db.Column(db.Numeric(4, 3), comment='3150Hz目标值')
    target_value_4000 = db.Column(db.Numeric(4, 3), comment='4000Hz目标值')
    target_value_5000 = db.Column(db.Numeric(4, 3), comment='5000Hz目标值')
    target_value_6300 = db.Column(db.Numeric(4, 3), comment='6300Hz目标值')
    target_value_8000 = db.Column(db.Numeric(4, 3), comment='8000Hz目标值')
    target_value_10000 = db.Column(db.Numeric(4, 3), comment='10000Hz目标值')
    
    # 测试信息
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    remarks = db.Column(db.Text, comment='备注')
    
    def __repr__(self):
        part_name = self.part.part_name if self.part else 'Unknown'
        material_name = self.material.material_name if self.material else 'Unknown'
        weight = self.material.weight if self.material else 'Unknown'
        return f'<SoundAbsorptionCoefficientModel {part_name}-{material_name}-{weight}g/m²>'
    
    @classmethod
    def get_frequency_columns(cls):
        """获取频率列名列表"""
        return [
            'test_value_125', 'test_value_160', 'test_value_200', 'test_value_250', 'test_value_315',
            'test_value_400', 'test_value_500', 'test_value_630', 'test_value_800', 'test_value_1000',
            'test_value_1250', 'test_value_1600', 'test_value_2000', 'test_value_2500', 'test_value_3150',
            'test_value_4000', 'test_value_5000', 'test_value_6300', 'test_value_8000', 'test_value_10000'
        ]
    
    @classmethod
    def get_target_frequency_columns(cls):
        """获取目标值频率列名列表"""
        return [
            'target_value_125', 'target_value_160', 'target_value_200', 'target_value_250', 'target_value_315',
            'target_value_400', 'target_value_500', 'target_value_630', 'target_value_800', 'target_value_1000',
            'target_value_1250', 'target_value_1600', 'target_value_2000', 'target_value_2500', 'target_value_3150',
            'target_value_4000', 'target_value_5000', 'target_value_6300', 'target_value_8000', 'target_value_10000'
        ]
    
    @classmethod
    def get_frequency_labels(cls):
        """获取频率标签列表"""
        return [
            '125Hz', '160Hz', '200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
            '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz', '6300Hz', '8000Hz', '10000Hz'
        ]
    
    @classmethod
    def get_weights_by_part_material(cls, part_name, material_name):
        """根据零件和材料获取克重列表（兼容旧接口）"""
        # 通过关联查询获取克重
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_weights_by_part_material_id(cls, part_id, material_name):
        """根据零件ID和材料名称获取克重列表"""
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                cls.part_id == part_id,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_data_by_conditions(cls, part_name, material_name, weight):
        """根据条件获取数据（兼容旧接口）"""
        return db.session.query(cls).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).join(
            MaterialModel, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name,
                MaterialModel.weight == weight
            )
        ).first()

    @classmethod
    def get_data_by_ids(cls, part_id, material_id):
        """根据ID获取数据"""
        return cls.query.filter(
            and_(cls.part_id == part_id, cls.material_id == material_id)
        ).first()
    
    def get_test_frequency_data(self):
        """获取测试值频率数据字典"""
        freq_columns = self.get_frequency_columns()
        freq_labels = self.get_frequency_labels()
        
        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None
        
        return data
    
    def get_target_frequency_data(self):
        """获取目标值频率数据字典"""
        freq_columns = self.get_target_frequency_columns()
        freq_labels = self.get_frequency_labels()
        
        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None
        
        return data
    
    def to_dict_with_frequency_data(self):
        """转换为包含频率数据的字典"""
        result = self.to_dict()
        result['test_frequency_data'] = self.get_test_frequency_data()
        result['target_frequency_data'] = self.get_target_frequency_data()

        # 添加关联数据
        if self.part:
            result['part_name'] = self.part.part_name
            result['part_description'] = self.part.description

        if self.material:
            result['material_name'] = self.material.material_name
            result['thickness'] = float(self.material.thickness) if self.material.thickness else None
            result['weight'] = float(self.material.weight) if self.material.weight else None
            result['material_description'] = self.material.description

        if self.manufacturer:
            result['manufacturer_name'] = self.manufacturer.manufacturer_name
            result['manufacturer_description'] = self.manufacturer.description

        return result

    @property
    def part_name(self):
        """获取零件名称（兼容性属性）"""
        return self.part.part_name if self.part else None

    @property
    def material_name(self):
        """获取材料名称（兼容性属性）"""
        return self.material.material_name if self.material else None

    @property
    def thickness(self):
        """获取厚度（兼容性属性）"""
        return self.material.thickness if self.material else None

    @property
    def weight(self):
        """获取克重（兼容性属性）"""
        return self.material.weight if self.material else None

    @property
    def manufacturer_name(self):
        """获取厂家名称（兼容性属性）"""
        return self.manufacturer.manufacturer_name if self.manufacturer else None


class SoundTransmissionLossModel(BaseModel):
    """隔声量模型"""
    __tablename__ = 'sound_transmission_loss'

    # 外键字段
    part_id = db.Column(db.Integer, db.ForeignKey('sound_insulation_parts.id'), nullable=False, comment='零件ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='材料ID')
    manufacturer_id = db.Column(db.Integer, db.ForeignKey('material_manufacturers.id'), comment='厂家ID')
    test_institution = db.Column(db.String(100), comment='测试机构')

    # 关系映射
    part = db.relationship('SoundInsulationPartModel', back_populates='transmission_losses', foreign_keys=[part_id])
    material = db.relationship('MaterialModel', back_populates='transmission_losses', foreign_keys=[material_id])
    manufacturer = db.relationship('MaterialManufacturerModel', back_populates='transmission_losses', foreign_keys=[manufacturer_id])

    # 测试值 (125-10000Hz中心频率，20个频率点)
    test_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz测试值(dB)')
    test_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz测试值(dB)')
    test_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz测试值(dB)')
    test_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz测试值(dB)')
    test_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz测试值(dB)')
    test_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz测试值(dB)')
    test_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz测试值(dB)')
    test_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz测试值(dB)')
    test_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz测试值(dB)')
    test_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz测试值(dB)')
    test_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz测试值(dB)')
    test_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz测试值(dB)')
    test_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz测试值(dB)')
    test_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz测试值(dB)')
    test_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz测试值(dB)')
    test_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz测试值(dB)')
    test_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz测试值(dB)')
    test_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz测试值(dB)')
    test_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz测试值(dB)')
    test_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz测试值(dB)')

    # 目标值 (125-10000Hz中心频率，20个频率点)
    target_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz目标值(dB)')
    target_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz目标值(dB)')
    target_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz目标值(dB)')
    target_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz目标值(dB)')
    target_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz目标值(dB)')
    target_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz目标值(dB)')
    target_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz目标值(dB)')
    target_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz目标值(dB)')
    target_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz目标值(dB)')
    target_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz目标值(dB)')
    target_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz目标值(dB)')
    target_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz目标值(dB)')
    target_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz目标值(dB)')
    target_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz目标值(dB)')
    target_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz目标值(dB)')
    target_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz目标值(dB)')
    target_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz目标值(dB)')
    target_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz目标值(dB)')
    target_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz目标值(dB)')
    target_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz目标值(dB)')

    # 测试信息
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    remarks = db.Column(db.Text, comment='备注')

    def __repr__(self):
        part_name = self.part.part_name if self.part else 'Unknown'
        material_name = self.material.material_name if self.material else 'Unknown'
        weight = self.material.weight if self.material else 'Unknown'
        return f'<SoundTransmissionLossModel {part_name}-{material_name}-{weight}g/m²>'

    @classmethod
    def get_frequency_columns(cls):
        """获取频率列名列表"""
        return [
            'test_value_125', 'test_value_160', 'test_value_200', 'test_value_250', 'test_value_315',
            'test_value_400', 'test_value_500', 'test_value_630', 'test_value_800', 'test_value_1000',
            'test_value_1250', 'test_value_1600', 'test_value_2000', 'test_value_2500', 'test_value_3150',
            'test_value_4000', 'test_value_5000', 'test_value_6300', 'test_value_8000', 'test_value_10000'
        ]

    @classmethod
    def get_target_frequency_columns(cls):
        """获取目标值频率列名列表"""
        return [
            'target_value_125', 'target_value_160', 'target_value_200', 'target_value_250', 'target_value_315',
            'target_value_400', 'target_value_500', 'target_value_630', 'target_value_800', 'target_value_1000',
            'target_value_1250', 'target_value_1600', 'target_value_2000', 'target_value_2500', 'target_value_3150',
            'target_value_4000', 'target_value_5000', 'target_value_6300', 'target_value_8000', 'target_value_10000'
        ]

    @classmethod
    def get_frequency_labels(cls):
        """获取频率标签列表"""
        return [
            '125Hz', '160Hz', '200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
            '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz', '6300Hz', '8000Hz', '10000Hz'
        ]

    @classmethod
    def get_weights_by_part_material(cls, part_name, material_name):
        """根据零件和材料获取克重列表"""
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_weights_by_part_material_id(cls, part_id, material_name):
        """根据零件ID和材料名称获取克重列表"""
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                cls.part_id == part_id,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_data_by_conditions(cls, part_name, material_name, weight):
        """根据条件获取数据"""
        return db.session.query(cls).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).join(
            MaterialModel, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name,
                MaterialModel.weight == weight
            )
        ).first()

    @classmethod
    def get_data_by_ids(cls, part_id, material_id):
        """根据ID获取数据"""
        return cls.query.filter(
            and_(cls.part_id == part_id, cls.material_id == material_id)
        ).first()

    def get_test_frequency_data(self):
        """获取测试值频率数据字典"""
        freq_columns = self.get_frequency_columns()
        freq_labels = self.get_frequency_labels()

        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None

        return data

    def get_target_frequency_data(self):
        """获取目标值频率数据字典"""
        freq_columns = self.get_target_frequency_columns()
        freq_labels = self.get_frequency_labels()

        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None

        return data

    def to_dict_with_frequency_data(self):
        """转换为包含频率数据的字典"""
        result = self.to_dict()
        result['test_frequency_data'] = self.get_test_frequency_data()
        result['target_frequency_data'] = self.get_target_frequency_data()

        # 添加关联数据
        if self.part:
            result['part_name'] = self.part.part_name
            result['part_description'] = self.part.description

        if self.material:
            result['material_name'] = self.material.material_name
            result['thickness'] = float(self.material.thickness) if self.material.thickness else None
            result['weight'] = float(self.material.weight) if self.material.weight else None
            result['material_description'] = self.material.description

        if self.manufacturer:
            result['manufacturer_name'] = self.manufacturer.manufacturer_name
            result['manufacturer_description'] = self.manufacturer.description

        return result

    @property
    def part_name(self):
        """获取零件名称（兼容性属性）"""
        return self.part.part_name if self.part else None

    @property
    def material_name(self):
        """获取材料名称（兼容性属性）"""
        return self.material.material_name if self.material else None

    @property
    def thickness(self):
        """获取厚度（兼容性属性）"""
        return self.material.thickness if self.material else None

    @property
    def weight(self):
        """获取克重（兼容性属性）"""
        return self.material.weight if self.material else None

    @property
    def manufacturer_name(self):
        """获取厂家名称（兼容性属性）"""
        return self.manufacturer.manufacturer_name if self.manufacturer else None


class WallMountedTransmissionLossModel(BaseModel):
    """上墙法隔声量模型"""
    __tablename__ = 'wall_mounted_transmission_loss'

    # 外键字段（复用现有表）
    part_id = db.Column(db.Integer, db.ForeignKey('sound_insulation_parts.id'), nullable=False, comment='零件ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='材料ID')
    manufacturer_id = db.Column(db.Integer, db.ForeignKey('material_manufacturers.id'), comment='厂家ID')
    test_institution = db.Column(db.String(100), comment='测试机构')

    # 关系映射
    part = db.relationship('SoundInsulationPartModel', foreign_keys=[part_id], overlaps="wall_mounted_transmission_losses")
    material = db.relationship('MaterialModel', foreign_keys=[material_id], overlaps="wall_mounted_transmission_losses")
    manufacturer = db.relationship('MaterialManufacturerModel', foreign_keys=[manufacturer_id], overlaps="wall_mounted_transmission_losses")

    # 测试值 (125-10000Hz中心频率，20个频率点)
    test_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz测试值(dB)')
    test_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz测试值(dB)')
    test_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz测试值(dB)')
    test_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz测试值(dB)')
    test_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz测试值(dB)')
    test_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz测试值(dB)')
    test_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz测试值(dB)')
    test_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz测试值(dB)')
    test_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz测试值(dB)')
    test_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz测试值(dB)')
    test_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz测试值(dB)')
    test_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz测试值(dB)')
    test_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz测试值(dB)')
    test_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz测试值(dB)')
    test_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz测试值(dB)')
    test_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz测试值(dB)')
    test_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz测试值(dB)')
    test_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz测试值(dB)')
    test_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz测试值(dB)')
    test_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz测试值(dB)')

    # 目标值 (125-10000Hz中心频率，20个频率点)
    target_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz目标值(dB)')
    target_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz目标值(dB)')
    target_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz目标值(dB)')
    target_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz目标值(dB)')
    target_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz目标值(dB)')
    target_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz目标值(dB)')
    target_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz目标值(dB)')
    target_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz目标值(dB)')
    target_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz目标值(dB)')
    target_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz目标值(dB)')
    target_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz目标值(dB)')
    target_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz目标值(dB)')
    target_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz目标值(dB)')
    target_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz目标值(dB)')
    target_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz目标值(dB)')
    target_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz目标值(dB)')
    target_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz目标值(dB)')
    target_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz目标值(dB)')
    target_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz目标值(dB)')
    target_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz目标值(dB)')

    # 测试信息
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    remarks = db.Column(db.Text, comment='备注')

    def __repr__(self):
        part_name = self.part.part_name if self.part else 'Unknown'
        material_name = self.material.material_name if self.material else 'Unknown'
        weight = self.material.weight if self.material else 'Unknown'
        return f'<WallMountedTransmissionLossModel {part_name}-{material_name}-{weight}g/m²>'

    @classmethod
    def get_frequency_columns(cls):
        """获取频率列名列表"""
        return [
            'test_value_125', 'test_value_160', 'test_value_200', 'test_value_250', 'test_value_315',
            'test_value_400', 'test_value_500', 'test_value_630', 'test_value_800', 'test_value_1000',
            'test_value_1250', 'test_value_1600', 'test_value_2000', 'test_value_2500', 'test_value_3150',
            'test_value_4000', 'test_value_5000', 'test_value_6300', 'test_value_8000', 'test_value_10000'
        ]

    @classmethod
    def get_target_frequency_columns(cls):
        """获取目标值频率列名列表"""
        return [
            'target_value_125', 'target_value_160', 'target_value_200', 'target_value_250', 'target_value_315',
            'target_value_400', 'target_value_500', 'target_value_630', 'target_value_800', 'target_value_1000',
            'target_value_1250', 'target_value_1600', 'target_value_2000', 'target_value_2500', 'target_value_3150',
            'target_value_4000', 'target_value_5000', 'target_value_6300', 'target_value_8000', 'target_value_10000'
        ]

    @classmethod
    def get_frequency_labels(cls):
        """获取频率标签列表"""
        return [
            '125Hz', '160Hz', '200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
            '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz', '6300Hz', '8000Hz', '10000Hz'
        ]

    @classmethod
    def get_weights_by_part_material(cls, part_name, material_name):
        """根据零件和材料获取克重列表"""
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_weights_by_part_material_id(cls, part_id, material_name):
        """根据零件ID和材料名称获取克重列表"""
        weights = db.session.query(MaterialModel.weight).join(
            cls, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                cls.part_id == part_id,
                MaterialModel.material_name == material_name
            )
        ).distinct().order_by(MaterialModel.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_data_by_conditions(cls, part_name, material_name, weight):
        """根据条件获取数据"""
        return db.session.query(cls).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).join(
            MaterialModel, MaterialModel.id == cls.material_id
        ).filter(
            and_(
                SoundInsulationPartModel.part_name == part_name,
                MaterialModel.material_name == material_name,
                MaterialModel.weight == weight
            )
        ).first()

    @classmethod
    def get_data_by_ids(cls, part_id, material_id):
        """根据ID获取数据"""
        return cls.query.filter(
            and_(cls.part_id == part_id, cls.material_id == material_id)
        ).first()

    def get_test_frequency_data(self):
        """获取测试值频率数据字典"""
        freq_columns = self.get_frequency_columns()
        freq_labels = self.get_frequency_labels()

        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None

        return data

    def get_target_frequency_data(self):
        """获取目标值频率数据字典"""
        freq_columns = self.get_target_frequency_columns()
        freq_labels = self.get_frequency_labels()

        data = {}
        for i, column in enumerate(freq_columns):
            value = getattr(self, column)
            data[freq_labels[i]] = float(value) if value is not None else None

        return data

    def to_dict_with_frequency_data(self):
        """转换为包含频率数据的字典"""
        result = self.to_dict()
        result['test_frequency_data'] = self.get_test_frequency_data()
        result['target_frequency_data'] = self.get_target_frequency_data()

        # 添加关联数据
        if self.part:
            result['part_name'] = self.part.part_name
            result['part_description'] = self.part.description

        if self.material:
            result['material_name'] = self.material.material_name
            result['thickness'] = float(self.material.thickness) if self.material.thickness else None
            result['weight'] = float(self.material.weight) if self.material.weight else None
            result['material_description'] = self.material.description

        if self.manufacturer:
            result['manufacturer_name'] = self.manufacturer.manufacturer_name
            result['manufacturer_description'] = self.manufacturer.description

        return result

    @property
    def part_name(self):
        """获取零件名称（兼容性属性）"""
        return self.part.part_name if self.part else None

    @property
    def material_name(self):
        """获取材料名称（兼容性属性）"""
        return self.material.material_name if self.material else None

    @property
    def thickness(self):
        """获取厚度（兼容性属性）"""
        return self.material.thickness if self.material else None

    @property
    def weight(self):
        """获取克重（兼容性属性）"""
        return self.material.weight if self.material else None

    @property
    def manufacturer_name(self):
        """获取厂家名称（兼容性属性）"""
        return self.manufacturer.manufacturer_name if self.manufacturer else None


class MaterialPorosityFlowResistanceModel(BaseModel):
    """材料孔隙率流阻模型"""
    __tablename__ = 'material_porosity_flow_resistance'

    # 外键字段（复用现有表）
    part_id = db.Column(db.Integer, db.ForeignKey('sound_insulation_parts.id'), nullable=False, comment='零件ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='材料ID')

    # 孔隙率流阻数据字段
    density = db.Column(db.Numeric(8, 3), comment='密度(kg/m³)')
    porosity = db.Column(db.Numeric(5, 3), comment='孔隙率(%)')
    porosity_deviation = db.Column(db.Numeric(5, 3), comment='孔隙率偏差(%)')
    flow_resistance = db.Column(db.Numeric(10, 2), comment='流阻率(Pa·s/m²)')
    flow_resistance_deviation = db.Column(db.Numeric(10, 2), comment='流阻率偏差(Pa·s/m²)')

    # 测试信息
    test_institution = db.Column(db.String(100), comment='测试机构')
    test_date = db.Column(db.Date, comment='测试日期')
    remarks = db.Column(db.Text, comment='备注')

    # 关系映射
    part = db.relationship('SoundInsulationPartModel', foreign_keys=[part_id])
    material = db.relationship('MaterialModel', foreign_keys=[material_id])

    # 唯一约束：确保每个零件-材料组合唯一
    __table_args__ = (
        db.UniqueConstraint('part_id', 'material_id', name='uk_part_material_porosity'),
    )

    def __repr__(self):
        part_name = self.part.part_name if self.part else 'Unknown'
        material_name = self.material.material_name if self.material else 'Unknown'
        return f'<MaterialPorosityFlowResistanceModel {part_name}-{material_name}>'

    @classmethod
    def get_data_by_conditions(cls, part_names=None, material_names=None):
        """根据条件获取数据"""
        query = db.session.query(cls).join(
            SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
        ).join(
            MaterialModel, MaterialModel.id == cls.material_id
        )

        if part_names:
            query = query.filter(SoundInsulationPartModel.part_name.in_(part_names))

        if material_names:
            query = query.filter(MaterialModel.material_name.in_(material_names))

        return query.order_by(
            SoundInsulationPartModel.part_name,
            MaterialModel.material_name
        ).all()

    @classmethod
    def get_parts_with_data(cls):
        """获取有孔隙率流阻数据的零件列表"""
        return db.session.query(SoundInsulationPartModel).join(
            cls, SoundInsulationPartModel.id == cls.part_id
        ).distinct().order_by(SoundInsulationPartModel.part_name).all()

    @classmethod
    def get_materials_with_data(cls, part_names=None):
        """获取有孔隙率流阻数据的材料列表"""
        query = db.session.query(MaterialModel).join(
            cls, MaterialModel.id == cls.material_id
        )

        if part_names:
            query = query.join(
                SoundInsulationPartModel, SoundInsulationPartModel.id == cls.part_id
            ).filter(SoundInsulationPartModel.part_name.in_(part_names))

        return query.distinct().order_by(MaterialModel.material_name).all()

    def to_dict_with_related_data(self):
        """转换为包含关联数据的字典"""
        result = self.to_dict()

        # 添加关联数据
        if self.part:
            result['part_name'] = self.part.part_name
            result['part_description'] = self.part.description

        if self.material:
            result['material_name'] = self.material.material_name
            result['thickness'] = float(self.material.thickness) if self.material.thickness else None
            result['weight'] = float(self.material.weight) if self.material.weight else None
            result['material_description'] = self.material.description

        # 转换数值字段为浮点数
        numeric_fields = ['density', 'porosity', 'porosity_deviation', 'flow_resistance', 'flow_resistance_deviation']
        for field in numeric_fields:
            value = getattr(self, field)
            result[field] = float(value) if value is not None else None

        return result

    @property
    def part_name(self):
        """获取零件名称（兼容性属性）"""
        return self.part.part_name if self.part else None

    @property
    def material_name(self):
        """获取材料名称（兼容性属性）"""
        return self.material.material_name if self.material else None

    @property
    def thickness(self):
        """获取厚度（兼容性属性）"""
        return self.material.thickness if self.material else None

    @property
    def weight(self):
        """获取克重（兼容性属性）"""
        return self.material.weight if self.material else None
