from models.base_model import BaseModel, db

class SoundInsulationAreaModel(BaseModel):
    """吸隔声测试区域模型"""
    __tablename__ = 'sound_insulation_areas'
    
    area_name = db.Column(db.String(50), nullable=False, comment='区域名称')
    description = db.Column(db.Text, comment='区域描述')
    
    # 反向关联关系
    sound_insulation_data = db.relationship('SoundInsulationDataModel', backref='area', lazy='dynamic')
    
    def __repr__(self):
        return f'<SoundInsulationAreaModel {self.area_name}>'
    
    @classmethod
    def get_all_areas(cls):
        """获取所有活跃的测试区域"""
        return cls.query.all()
    
    @classmethod
    def get_area_by_id(cls, area_id):
        """根据ID获取区域"""
        return cls.query.get(area_id)
    
    @classmethod
    def get_area_by_name(cls, area_name):
        """根据名称获取区域"""
        return cls.query.filter_by(area_name=area_name).first()
