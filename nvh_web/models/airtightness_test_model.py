from models.base_model import BaseModel, db

class AirtightnessTestModel(BaseModel):
    """气密性测试模型"""
    __tablename__ = 'airtightness_tests'
    
    vehicle_model_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('vehicle_models.id'), nullable=False, comment='车型ID')
    test_date = db.Column(db.Date, nullable=False, comment='测试日期')
    test_engineer = db.<PERSON>umn(db.String(50), nullable=False, comment='测试工程师')
    test_location = db.Column(db.String(100), comment='测试地点')
    
    # 泄漏量数据字段 (SCFM)
    uncontrolled_leakage = db.Column(db.Numeric(8, 1), comment='整车不可控泄漏量')
    left_pressure_valve = db.Column(db.Numeric(8, 1), comment='左侧泄压阀')
    right_pressure_valve = db.Column(db.Numeric(8, 1), comment='右侧泄压阀')
    ac_circulation_valve = db.Column(db.Numeric(8, 1), comment='空调内外循环阀')
    right_door_drain_hole = db.Column(db.Numeric(8, 1), comment='右侧门漏液孔')
    tailgate_drain_hole = db.Column(db.Numeric(8, 1), comment='尾门漏液孔')
    right_door_outer_seal = db.Column(db.Numeric(8, 1), comment='右侧门外水切')
    right_door_outer_opening = db.Column(db.Numeric(8, 1), comment='右侧门外开')
    side_mirrors = db.Column(db.Numeric(8, 1), comment='两侧外后视镜')
    body_shell_leakage = db.Column(db.Numeric(8, 1), comment='白车身泄漏量')
    other_area = db.Column(db.Numeric(8, 1), comment='其他区域')
    
    notes = db.Column(db.Text, comment='备注')
    updated_by = db.Column(db.String(50), comment='修改人员')
    
    # 关联关系
    vehicle_model = db.relationship('VehicleModel', backref='airtightness_tests')
    
    def __repr__(self):
        return f'<AirtightnessTestModel {self.vehicle_model_id} {self.test_date}>'
    
    def get_leakage_data(self):
        """获取泄漏量数据字典"""
        return {
            'uncontrolled_leakage': float(self.uncontrolled_leakage) if self.uncontrolled_leakage else None,
            'left_pressure_valve': float(self.left_pressure_valve) if self.left_pressure_valve else None,
            'right_pressure_valve': float(self.right_pressure_valve) if self.right_pressure_valve else None,
            'ac_circulation_valve': float(self.ac_circulation_valve) if self.ac_circulation_valve else None,
            'right_door_drain_hole': float(self.right_door_drain_hole) if self.right_door_drain_hole else None,
            'tailgate_drain_hole': float(self.tailgate_drain_hole) if self.tailgate_drain_hole else None,
            'right_door_outer_seal': float(self.right_door_outer_seal) if self.right_door_outer_seal else None,
            'right_door_outer_opening': float(self.right_door_outer_opening) if self.right_door_outer_opening else None,
            'side_mirrors': float(self.side_mirrors) if self.side_mirrors else None,
            'body_shell_leakage': float(self.body_shell_leakage) if self.body_shell_leakage else None,
            'other_area': float(self.other_area) if self.other_area else None
        }
    
    @staticmethod
    def get_area_config():
        """获取测试区域配置"""
        return {
            '整车不可控泄漏量': [
                {'name': '', 'field': 'uncontrolled_leakage'}
            ],
            '阀系统': [
                {'name': '左侧泄压阀', 'field': 'left_pressure_valve'},
                {'name': '右侧泄压阀', 'field': 'right_pressure_valve'},
                {'name': '空调内外循环阀', 'field': 'ac_circulation_valve'}
            ],
            '门系统': [
                {'name': '右侧门漏液孔', 'field': 'right_door_drain_hole'},
                {'name': '尾门漏液孔', 'field': 'tailgate_drain_hole'},
                {'name': '右侧门外水切', 'field': 'right_door_outer_seal'},
                {'name': '右侧门外开', 'field': 'right_door_outer_opening'},
                {'name': '两侧外后视镜', 'field': 'side_mirrors'}
            ],
            '白车身': [
                {'name': '白车身泄漏量', 'field': 'body_shell_leakage'}
            ],
            '其他区域': [
                {'name': '其他区域', 'field': 'other_area'}
            ]
        }
